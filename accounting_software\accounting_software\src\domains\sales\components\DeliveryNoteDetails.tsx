import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
} from '@mui/material';
import {
  LocalShipping as DeliveryIcon,
  CheckCircle as ValidateIcon,
  Print as PrintIcon,
  Edit as EditIcon,
  ArrowBack as BackIcon,
  Inventory as StockIcon,
  Receipt as InvoiceIcon,
} from '@mui/icons-material';

import { goodsDeliveryNoteService, GoodsDeliveryNote, GoodsDeliveryNoteLineItem } from '../../../services/goodsDeliveryNote.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const DeliveryNoteDetails: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getCurrencySymbol, formatCurrency } = useCurrencyInfo();

  // State
  const [deliveryNote, setDeliveryNote] = useState<GoodsDeliveryNote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [validateDialog, setValidateDialog] = useState(false);
  const [validationData, setValidationData] = useState({
    received_by: '',
    customer_signature: '',
  });

  useEffect(() => {
    if (id) {
      loadDeliveryNote();
    }
  }, [id]);

  const loadDeliveryNote = async () => {
    try {
      setLoading(true);
      const data = await goodsDeliveryNoteService.getById(parseInt(id!));
      setDeliveryNote(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load delivery note');
    } finally {
      setLoading(false);
    }
  };

  const handleValidateDelivery = async () => {
    if (!deliveryNote) return;

    try {
      await goodsDeliveryNoteService.confirmDelivery(deliveryNote.id!, validationData);
      setValidateDialog(false);
      loadDeliveryNote();
    } catch (err: any) {
      setError(err.message || 'Failed to validate delivery');
    }
  };

  const handleCreateInvoice = async () => {
    if (!deliveryNote) return;

    try {
      const invoice = await goodsDeliveryNoteService.createInvoice(deliveryNote.id!, {
        invoice_date: new Date().toISOString().split('T')[0],
        payment_terms: 'Net 30',
      });
      navigate(`/dashboard/sales/invoices/${invoice.id}`);
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      draft: 'default' as const,
      confirmed: 'warning' as const,
      delivered: 'success' as const,
      cancelled: 'error' as const,
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const calculateTotals = () => {
    if (!deliveryNote?.line_items) return { subtotal: 0, total: 0 };
    
    const subtotal = deliveryNote.line_items.reduce((sum, item) => 
      sum + (item.quantity_delivered * item.unit_price), 0
    );
    
    return { subtotal, total: subtotal }; // Add tax calculation if needed
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Loading delivery note...</Typography>
      </Box>
    );
  }

  if (error || !deliveryNote) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error || 'Delivery note not found'}</Alert>
      </Box>
    );
  }

  const { subtotal, total } = calculateTotals();

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            startIcon={<BackIcon />}
            onClick={() => navigate('/dashboard/sales/delivery-notes')}
            variant="outlined"
          >
            Back
          </Button>
          <DeliveryIcon sx={{ fontSize: 32, color: 'primary.main' }} />
          <Box>
            <Typography variant="h4">{deliveryNote.gdn_number}</Typography>
            <Chip
              label={deliveryNote.status}
              color={getStatusColor(deliveryNote.status)}
              sx={{ textTransform: 'capitalize', mt: 1 }}
            />
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          {deliveryNote.status === 'confirmed' && (
            <Button
              variant="contained"
              startIcon={<ValidateIcon />}
              onClick={() => setValidateDialog(true)}
              sx={{ bgcolor: '#4caf50', '&:hover': { bgcolor: '#45a049' } }}
            >
              Validate Delivery
            </Button>
          )}
          {deliveryNote.status === 'delivered' && (
            <Button
              variant="contained"
              startIcon={<InvoiceIcon />}
              onClick={handleCreateInvoice}
            >
              Create Invoice
            </Button>
          )}
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/dashboard/sales/delivery-notes/${id}/edit`)}
            disabled={deliveryNote.status === 'delivered'}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={() => window.print()}
          >
            Print
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Delivery Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Delivery Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Sales Order
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.sales_order_number}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Customer
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.customer_name}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Delivery Date
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {new Date(deliveryNote.delivery_date).toLocaleDateString()}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Expected Date
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.expected_delivery_date 
                      ? new Date(deliveryNote.expected_delivery_date).toLocaleDateString()
                      : '-'
                    }
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Delivery Details */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Delivery Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Driver
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.driver_name || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Vehicle
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.vehicle_number || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Contact Person
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.delivery_contact_person || '-'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Contact Phone
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {deliveryNote.delivery_contact_phone || '-'}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Delivery Address */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Delivery Address
              </Typography>
              <Typography variant="body1">
                {deliveryNote.delivery_address || 'No delivery address specified'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Line Items */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Items to Deliver
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="right">Ordered</TableCell>
                      <TableCell align="right">Delivered</TableCell>
                      <TableCell align="right">Remaining</TableCell>
                      <TableCell align="right">Unit Price</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {deliveryNote.line_items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            Product #{item.product}
                          </Typography>
                        </TableCell>
                        <TableCell>{item.description}</TableCell>
                        <TableCell align="right">{item.quantity_ordered}</TableCell>
                        <TableCell align="right">
                          <Typography
                            variant="body2"
                            color={item.quantity_delivered > 0 ? 'success.main' : 'text.secondary'}
                            fontWeight="medium"
                          >
                            {item.quantity_delivered}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{item.quantity_remaining}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(item.unit_price)}
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(item.quantity_delivered * item.unit_price)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Totals */}
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ minWidth: 200 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Subtotal:</Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {formatCurrency(subtotal)}
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6" color="primary">
                      {formatCurrency(total)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Notes */}
        {(deliveryNote.notes || deliveryNote.internal_notes) && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Notes
                </Typography>
                {deliveryNote.notes && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Delivery Notes:
                    </Typography>
                    <Typography variant="body1">{deliveryNote.notes}</Typography>
                  </Box>
                )}
                {deliveryNote.internal_notes && (
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Internal Notes:
                    </Typography>
                    <Typography variant="body1">{deliveryNote.internal_notes}</Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Validate Delivery Dialog */}
      <Dialog open={validateDialog} onClose={() => setValidateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Validate Delivery</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Confirm that the delivery has been completed and received by the customer.
          </Typography>
          <TextField
            fullWidth
            label="Received By"
            value={validationData.received_by}
            onChange={(e) => setValidationData(prev => ({ ...prev, received_by: e.target.value }))}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Customer Signature/Confirmation"
            value={validationData.customer_signature}
            onChange={(e) => setValidationData(prev => ({ ...prev, customer_signature: e.target.value }))}
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setValidateDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleValidateDelivery} 
            variant="contained"
            disabled={!validationData.received_by}
          >
            Validate Delivery
          </Button>
        </DialogActions>
      </Dialog>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default DeliveryNoteDetails;
