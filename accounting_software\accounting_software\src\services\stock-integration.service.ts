/**
 * Stock Integration Service for Odoo-style Sales Order Workflow
 * Handles stock reservation, delivery order creation, and inventory moves
 */

import api from './api';
import { OdooSalesOrder, DeliveryOrder, StockMove } from './odoo-sales-order.service';

export interface StockReservation {
  id?: number;
  product_id: number;
  quantity: number;
  reserved_quantity: number;
  location_id: number;
  sale_line_id: number;
  state: 'draft' | 'confirmed' | 'assigned' | 'done' | 'cancel';
}

export interface StockLocation {
  id: number;
  name: string;
  location_type: 'supplier' | 'view' | 'internal' | 'customer' | 'inventory' | 'procurement' | 'production' | 'transit';
  parent_id?: number;
  company_id: number;
}

export interface ProductStock {
  product_id: number;
  location_id: number;
  quantity_on_hand: number;
  quantity_reserved: number;
  quantity_available: number;
  incoming_quantity: number;
  outgoing_quantity: number;
}

class StockIntegrationService {
  private baseUrl = '/api/inventory';

  /**
   * Check product availability for sales order lines
   */
  async checkProductAvailability(lines: { product_id: number; quantity: number; location_id?: number }[]): Promise<{
    available: boolean;
    details: { product_id: number; requested: number; available: number; shortage: number }[];
  }> {
    const response = await api.post(`${this.baseUrl}/check-availability/`, { lines });
    return response.data;
  }

  /**
   * Reserve stock for sales order (Odoo's action_confirm equivalent)
   */
  async reserveStockForSalesOrder(salesOrderId: number): Promise<{
    success: boolean;
    reservations: StockReservation[];
    warnings: string[];
  }> {
    const response = await api.post(`${this.baseUrl}/reserve-stock/`, {
      sales_order_id: salesOrderId,
    });
    return response.data;
  }

  /**
   * Create delivery order from sales order (automatic on confirmation)
   */
  async createDeliveryOrderFromSalesOrder(salesOrderId: number, options?: {
    location_id?: number;
    location_dest_id?: number;
    scheduled_date?: string;
    delivery_policy?: 'direct' | 'one';
  }): Promise<DeliveryOrder> {
    const response = await api.post(`${this.baseUrl}/create-delivery-order/`, {
      sales_order_id: salesOrderId,
      ...options,
    });
    return response.data;
  }

  /**
   * Get stock moves for a delivery order
   */
  async getStockMoves(deliveryOrderId: number): Promise<StockMove[]> {
    const response = await api.get(`${this.baseUrl}/delivery-orders/${deliveryOrderId}/moves/`);
    return response.data.results || response.data;
  }

  /**
   * Update stock move quantities (for partial deliveries)
   */
  async updateStockMove(moveId: number, data: {
    quantity_done?: number;
    location_id?: number;
    location_dest_id?: number;
  }): Promise<StockMove> {
    const response = await api.put(`${this.baseUrl}/stock-moves/${moveId}/`, data);
    return response.data;
  }

  /**
   * Validate delivery order (mark as done)
   */
  async validateDeliveryOrder(deliveryOrderId: number, moves?: {
    move_id: number;
    quantity_done: number;
  }[]): Promise<{
    delivery_order: DeliveryOrder;
    updated_sales_order: OdooSalesOrder;
    stock_moves: StockMove[];
  }> {
    const response = await api.post(`${this.baseUrl}/delivery-orders/${deliveryOrderId}/validate/`, {
      moves: moves || [],
    });
    return response.data;
  }

  /**
   * Cancel delivery order and unreserve stock
   */
  async cancelDeliveryOrder(deliveryOrderId: number): Promise<{
    success: boolean;
    unreserved_stock: StockReservation[];
  }> {
    const response = await api.post(`${this.baseUrl}/delivery-orders/${deliveryOrderId}/cancel/`);
    return response.data;
  }

  /**
   * Get product stock levels by location
   */
  async getProductStock(productId: number, locationId?: number): Promise<ProductStock[]> {
    const params = new URLSearchParams();
    if (locationId) params.append('location_id', locationId.toString());
    
    const response = await api.get(`${this.baseUrl}/products/${productId}/stock/?${params.toString()}`);
    return response.data.results || response.data;
  }

  /**
   * Get all stock locations
   */
  async getStockLocations(companyId?: number): Promise<StockLocation[]> {
    const params = new URLSearchParams();
    if (companyId) params.append('company_id', companyId.toString());
    
    const response = await api.get(`${this.baseUrl}/locations/?${params.toString()}`);
    return response.data.results || response.data;
  }

  /**
   * Create stock adjustment (for inventory corrections)
   */
  async createStockAdjustment(data: {
    product_id: number;
    location_id: number;
    quantity_adjustment: number;
    reason?: string;
  }): Promise<any> {
    const response = await api.post(`${this.baseUrl}/adjustments/`, data);
    return response.data;
  }

  /**
   * Get stock movements history
   */
  async getStockMovements(filters?: {
    product_id?: number;
    location_id?: number;
    date_from?: string;
    date_to?: string;
    move_type?: 'in' | 'out' | 'internal';
  }): Promise<any[]> {
    const params = new URLSearchParams();
    if (filters?.product_id) params.append('product_id', filters.product_id.toString());
    if (filters?.location_id) params.append('location_id', filters.location_id.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.move_type) params.append('move_type', filters.move_type);
    
    const response = await api.get(`${this.baseUrl}/movements/?${params.toString()}`);
    return response.data.results || response.data;
  }

  /**
   * Odoo-style workflow: Sales Order Confirmation Process
   * This method orchestrates the entire stock reservation and delivery creation process
   */
  async processSalesOrderConfirmation(salesOrderId: number): Promise<{
    success: boolean;
    sales_order: OdooSalesOrder;
    delivery_orders: DeliveryOrder[];
    stock_reservations: StockReservation[];
    warnings: string[];
    errors: string[];
  }> {
    try {
      // Step 1: Check stock availability
      const salesOrder = await api.get(`/api/sales/orders/${salesOrderId}/`);
      const lines = salesOrder.data.order_line.map((line: any) => ({
        product_id: line.product_id,
        quantity: line.product_uom_qty,
      }));

      const availability = await this.checkProductAvailability(lines);
      const warnings: string[] = [];
      const errors: string[] = [];

      // Step 2: Handle stock shortages
      availability.details.forEach(detail => {
        if (detail.shortage > 0) {
          warnings.push(`Product ${detail.product_id}: ${detail.shortage} units short`);
        }
      });

      // Step 3: Reserve available stock
      const reservationResult = await this.reserveStockForSalesOrder(salesOrderId);
      warnings.push(...reservationResult.warnings);

      // Step 4: Create delivery orders based on delivery policy
      const deliveryPolicy = salesOrder.data.delivery_policy || 'direct';
      let deliveryOrders: DeliveryOrder[] = [];

      if (deliveryPolicy === 'direct') {
        // Create delivery for available items immediately
        const deliveryOrder = await this.createDeliveryOrderFromSalesOrder(salesOrderId, {
          delivery_policy: 'direct',
        });
        deliveryOrders.push(deliveryOrder);
      } else {
        // Wait for all items to be available
        if (availability.available) {
          const deliveryOrder = await this.createDeliveryOrderFromSalesOrder(salesOrderId, {
            delivery_policy: 'one',
          });
          deliveryOrders.push(deliveryOrder);
        } else {
          warnings.push('Delivery order will be created when all products are available');
        }
      }

      // Step 5: Update sales order state
      const updatedSalesOrder = await api.put(`/api/sales/orders/${salesOrderId}/`, {
        state: 'sale',
        delivery_count: deliveryOrders.length,
      });

      return {
        success: true,
        sales_order: updatedSalesOrder.data,
        delivery_orders: deliveryOrders,
        stock_reservations: reservationResult.reservations,
        warnings,
        errors,
      };

    } catch (error: any) {
      return {
        success: false,
        sales_order: {} as OdooSalesOrder,
        delivery_orders: [],
        stock_reservations: [],
        warnings: [],
        errors: [error.message || 'Failed to process sales order confirmation'],
      };
    }
  }

  /**
   * Get delivery status for sales order lines
   */
  async getDeliveryStatus(salesOrderId: number): Promise<{
    lines: {
      line_id: number;
      product_id: number;
      ordered_qty: number;
      delivered_qty: number;
      remaining_qty: number;
      delivery_status: 'nothing' | 'partial' | 'full';
    }[];
    overall_status: 'nothing' | 'partial' | 'full';
  }> {
    const response = await api.get(`/api/sales/orders/${salesOrderId}/delivery-status/`);
    return response.data;
  }

  /**
   * Auto-create invoice based on delivery (for invoice_policy = 'delivery')
   */
  async createInvoiceFromDelivery(deliveryOrderId: number): Promise<any> {
    const response = await api.post(`${this.baseUrl}/delivery-orders/${deliveryOrderId}/create-invoice/`);
    return response.data;
  }
}

export const stockIntegrationService = new StockIntegrationService();
export default stockIntegrationService;
