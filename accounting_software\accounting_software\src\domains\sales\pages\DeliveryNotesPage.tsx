import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  LocalShipping as DeliveryIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  CheckCircle as ValidateIcon,
  Cancel as CancelIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';

import { goodsDeliveryNoteService, GoodsDeliveryNote } from '../../../services/goodsDeliveryNote.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const DeliveryNotesPage: React.FC = () => {
  const navigate = useNavigate();
  const { getCurrencySymbol } = useCurrencyInfo();

  // State
  const [deliveryNotes, setDeliveryNotes] = useState<GoodsDeliveryNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedNote, setSelectedNote] = useState<GoodsDeliveryNote | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [deleteDialog, setDeleteDialog] = useState(false);

  // Stats
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    confirmed: 0,
    delivered: 0,
    cancelled: 0,
  });

  useEffect(() => {
    loadDeliveryNotes();
  }, [statusFilter]);

  const loadDeliveryNotes = async () => {
    try {
      setLoading(true);
      const filters = statusFilter !== 'all' ? { status: statusFilter } : {};
      const data = await goodsDeliveryNoteService.getAll(filters);
      setDeliveryNotes(data.results || data);
      calculateStats(data.results || data);
    } catch (err: any) {
      setError(err.message || 'Failed to load delivery notes');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (notes: GoodsDeliveryNote[]) => {
    const stats = {
      total: notes.length,
      draft: notes.filter(n => n.status === 'draft').length,
      confirmed: notes.filter(n => n.status === 'confirmed').length,
      delivered: notes.filter(n => n.status === 'delivered').length,
      cancelled: notes.filter(n => n.status === 'cancelled').length,
    };
    setStats(stats);
  };

  const handleActionClick = (event: React.MouseEvent<HTMLElement>, note: GoodsDeliveryNote) => {
    setSelectedNote(note);
    setActionMenuAnchor(event.currentTarget);
  };

  const handleActionClose = () => {
    setActionMenuAnchor(null);
    setSelectedNote(null);
  };

  const handleValidateDelivery = async (note: GoodsDeliveryNote) => {
    try {
      await goodsDeliveryNoteService.confirmDelivery(note.id!, {
        received_by: 'Customer',
        customer_signature: 'Received',
      });
      loadDeliveryNotes();
      handleActionClose();
    } catch (err: any) {
      setError(err.message || 'Failed to validate delivery');
    }
  };

  const handleDeleteNote = async () => {
    if (!selectedNote) return;
    
    try {
      await goodsDeliveryNoteService.delete(selectedNote.id!);
      loadDeliveryNotes();
      setDeleteDialog(false);
      handleActionClose();
    } catch (err: any) {
      setError(err.message || 'Failed to delete delivery note');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      draft: 'default' as const,
      confirmed: 'warning' as const,
      delivered: 'success' as const,
      cancelled: 'error' as const,
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const filteredNotes = deliveryNotes.filter(note =>
    note.gdn_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.sales_order_number?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderStatsCards = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6} md={2.4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="primary">{stats.total}</Typography>
            <Typography variant="body2" color="text.secondary">Total</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={2.4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="text.secondary">{stats.draft}</Typography>
            <Typography variant="body2" color="text.secondary">Draft</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={2.4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="warning.main">{stats.confirmed}</Typography>
            <Typography variant="body2" color="text.secondary">Confirmed</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={2.4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="success.main">{stats.delivered}</Typography>
            <Typography variant="body2" color="text.secondary">Delivered</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={2.4}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="error.main">{stats.cancelled}</Typography>
            <Typography variant="body2" color="text.secondary">Cancelled</Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <DeliveryIcon sx={{ fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4">Delivery Notes</Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/sales/delivery-notes/create')}
        >
          Create Delivery Note
        </Button>
      </Box>

      {/* Stats Cards */}
      {renderStatsCards()}

      {/* Filters and Search */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <TextField
          placeholder="Search delivery notes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 300 }}
        />
        <TextField
          select
          label="Status"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          sx={{ minWidth: 150 }}
        >
          <MenuItem value="all">All Status</MenuItem>
          <MenuItem value="draft">Draft</MenuItem>
          <MenuItem value="confirmed">Confirmed</MenuItem>
          <MenuItem value="delivered">Delivered</MenuItem>
          <MenuItem value="cancelled">Cancelled</MenuItem>
        </TextField>
      </Box>

      {/* Delivery Notes Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>GDN Number</TableCell>
                <TableCell>Sales Order</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Delivery Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Driver</TableCell>
                <TableCell>Vehicle</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    Loading delivery notes...
                  </TableCell>
                </TableRow>
              ) : filteredNotes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography color="text.secondary">
                      No delivery notes found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredNotes.map((note) => (
                  <TableRow key={note.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {note.gdn_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {note.sales_order_number}
                      </Typography>
                    </TableCell>
                    <TableCell>{note.customer_name}</TableCell>
                    <TableCell>
                      {new Date(note.delivery_date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={note.status}
                        color={getStatusColor(note.status)}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </TableCell>
                    <TableCell>{note.driver_name || '-'}</TableCell>
                    <TableCell>{note.vehicle_number || '-'}</TableCell>
                    <TableCell align="right">
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => navigate(`/dashboard/sales/delivery-notes/${note.id}`)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="More Actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleActionClick(e, note)}
                        >
                          <MoreIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={() => navigate(`/dashboard/sales/delivery-notes/${selectedNote?.id}`)}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => navigate(`/dashboard/sales/delivery-notes/${selectedNote?.id}/edit`)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        {selectedNote?.status === 'confirmed' && (
          <MenuItem onClick={() => handleValidateDelivery(selectedNote)}>
            <ValidateIcon sx={{ mr: 1 }} />
            Validate Delivery
          </MenuItem>
        )}
        <MenuItem onClick={() => window.print()}>
          <PrintIcon sx={{ mr: 1 }} />
          Print
        </MenuItem>
        <MenuItem 
          onClick={() => setDeleteDialog(true)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Delete Delivery Note</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete delivery note {selectedNote?.gdn_number}?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDeleteNote} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Error Display */}
      {error && (
        <Box sx={{ mt: 2 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}
    </Box>
  );
};

export default DeliveryNotesPage;
