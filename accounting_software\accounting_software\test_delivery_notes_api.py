#!/usr/bin/env python3
"""
Test script to verify delivery notes API endpoints
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api"

def get_auth_token():
    """Get authentication token"""
    try:
        response = requests.post(f"{BASE_URL}/api-token-auth/", {
            'username': 'admin',  # Replace with your admin username
            'password': 'admin123'  # Replace with your admin password
        })
        if response.status_code == 200:
            return response.json()['token']
        else:
            print(f"Failed to get token: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"Error getting token: {e}")
        return None

def test_delivery_notes_api():
    """Test delivery notes API endpoints"""
    print("🔍 Testing Delivery Notes API...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test 1: List delivery notes
    print("\n1. Testing GET /api/sales/delivery-notes/")
    try:
        response = requests.get(f"{API_URL}/sales/delivery-notes/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response type: {type(data)}")
            
            if isinstance(data, dict):
                print(f"   Keys: {list(data.keys())}")
                if 'results' in data:
                    print(f"   Results count: {len(data['results'])}")
                    if data['results']:
                        first_item = data['results'][0]
                        print(f"   First item keys: {list(first_item.keys())}")
                        print(f"   First item ID: {first_item.get('id', 'NO ID FIELD')}")
                        print(f"   First item: {json.dumps(first_item, indent=2)}")
                else:
                    print(f"   Direct list count: {len(data)}")
            elif isinstance(data, list):
                print(f"   List count: {len(data)}")
                if data:
                    first_item = data[0]
                    print(f"   First item keys: {list(first_item.keys())}")
                    print(f"   First item ID: {first_item.get('id', 'NO ID FIELD')}")
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 2: Try to get a specific delivery note (if any exist)
    print("\n2. Testing GET /api/sales/delivery-notes/1/")
    try:
        response = requests.get(f"{API_URL}/sales/delivery-notes/1/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Item keys: {list(data.keys())}")
            print(f"   Item ID: {data.get('id', 'NO ID FIELD')}")
        elif response.status_code == 404:
            print("   No delivery note with ID 1 found (expected if no data)")
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 3: Check available endpoints
    print("\n3. Testing API root to see available endpoints")
    try:
        response = requests.get(f"{API_URL}/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   Available endpoints:")
            for key, value in data.items():
                print(f"     {key}: {value}")
                
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 4: Check sales endpoints specifically
    print("\n4. Testing /api/sales/ endpoints")
    try:
        response = requests.get(f"{API_URL}/sales/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   Available sales endpoints:")
            for key, value in data.items():
                print(f"     {key}: {value}")
                
    except Exception as e:
        print(f"   Exception: {e}")

def create_test_delivery_note():
    """Create a test delivery note to verify the API works"""
    print("\n🔧 Creating test delivery note...")
    
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # First, let's check if we have any sales orders to link to
    print("   Checking for sales orders...")
    try:
        response = requests.get(f"{API_URL}/sales/sales-orders/", headers=headers)
        if response.status_code == 200:
            sales_orders = response.json()
            if isinstance(sales_orders, dict) and 'results' in sales_orders:
                sales_orders = sales_orders['results']
            
            if sales_orders:
                sales_order_id = sales_orders[0]['id']
                print(f"   Found sales order ID: {sales_order_id}")
                
                # Create test delivery note
                test_data = {
                    'sales_order': sales_order_id,
                    'delivery_date': '2024-01-15',
                    'delivery_address': 'Test Address',
                    'notes': 'Test delivery note created by API test'
                }
                
                response = requests.post(f"{API_URL}/sales/delivery-notes/", 
                                       json=test_data, headers=headers)
                print(f"   Create status: {response.status_code}")
                
                if response.status_code == 201:
                    created_note = response.json()
                    print(f"   Created delivery note ID: {created_note.get('id')}")
                    print(f"   Created note: {json.dumps(created_note, indent=2)}")
                else:
                    print(f"   Create error: {response.text}")
            else:
                print("   No sales orders found - cannot create delivery note")
        else:
            print(f"   Failed to get sales orders: {response.status_code}")
            
    except Exception as e:
        print(f"   Exception: {e}")

if __name__ == "__main__":
    print("🚀 Delivery Notes API Test")
    print("=" * 50)
    
    test_delivery_notes_api()
    create_test_delivery_note()
    
    print("\n✅ Test completed!")
