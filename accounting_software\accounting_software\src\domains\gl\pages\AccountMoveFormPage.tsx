/**
 * Odoo-style Account Move Form Page
 * Universal form for creating/editing all accounting documents
 * Following <PERSON><PERSON><PERSON>'s account.move form view patterns
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  IconButton,
  Divider,
  Paper,
  Alert,
  Snackbar,
  Avatar,
  Chip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  PostAdd as PostIcon,
  Cancel as CancelIcon,
  Receipt as ReceiptIcon,
  Description as JournalIcon,
  Assignment as BillIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { StandardDatePicker } from '../../../shared/components';
import { AccountMove, AccountMoveLine, AccountJournal } from '../../../shared/types/gl.types';
import { accountMoveService } from '../../../services/account-move.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';

interface AccountMoveFormData {
  name: string;
  move_type: 'entry' | 'out_invoice' | 'out_refund' | 'in_invoice' | 'in_refund' | 'out_receipt' | 'in_receipt';
  state: string;
  ref: string;
  date: string;
  partner_id: number | null;
  journal_id: number | null;
  line_ids: Partial<AccountMoveLine>[];
}

const AccountMoveFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const { currencyInfo } = useCurrencyInfo();
  
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [journals, setJournals] = useState<AccountJournal[]>([]);
  const [partners, setPartners] = useState<any[]>([]);
  const [accounts, setAccounts] = useState<any[]>([]);

  const [formData, setFormData] = useState<AccountMoveFormData>({
    name: '/',
    move_type: (searchParams.get('type') as AccountMoveFormData['move_type']) || 'entry',
    state: 'draft',
    ref: '',
    date: dayjs().format('YYYY-MM-DD'),
    partner_id: null,
    journal_id: null,
    line_ids: [
      {
        account_id: undefined,
        name: '',
        debit: '0.00',
        credit: '0.00',
      },
      {
        account_id: undefined,
        name: '',
        debit: '0.00',
        credit: '0.00',
      }
    ],
  });

  // Load data
  useEffect(() => {
    loadInitialData();
    if (id) {
      loadAccountMove();
    }
  }, [id]);

  const loadInitialData = async () => {
    try {
      const [journalsResponse, accountsResponse] = await Promise.all([
        accountMoveService.getJournals(),
        accountMoveService.getAccounts(),
      ]);
      
      setJournals(journalsResponse);
      setAccounts(accountsResponse);
      
      // Set default journal based on move type
      if (journalsResponse.length > 0 && !formData.journal_id) {
        const defaultJournal = getDefaultJournal(formData.move_type, journalsResponse);
        if (defaultJournal) {
          setFormData(prev => ({ ...prev, journal_id: defaultJournal.id }));
        }
      }
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load form data');
    }
  };

  const loadAccountMove = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const move = await accountMoveService.getAccountMove(parseInt(id));
      
      setFormData({
        name: move.name,
        move_type: move.move_type,
        state: move.state,
        ref: move.ref || '',
        date: move.date,
        partner_id: move.partner_id || null,
        journal_id: move.journal_id,
        line_ids: move.line_ids || [],
      });
    } catch (err) {
      console.error('Error loading account move:', err);
      setError('Failed to load account move');
    } finally {
      setLoading(false);
    }
  };

  const getDefaultJournal = (moveType: string, journals: AccountJournal[]): AccountJournal | null => {
    const typeMapping: { [key: string]: string } = {
      'entry': 'general',
      'out_invoice': 'sale',
      'out_refund': 'sale',
      'in_invoice': 'purchase',
      'in_refund': 'purchase',
    };
    
    const journalType = typeMapping[moveType];
    return journals.find(j => j.type === journalType) || journals[0] || null;
  };

  const getMoveTypeIcon = (moveType: string) => {
    switch (moveType) {
      case 'entry': return <JournalIcon />;
      case 'out_invoice': case 'out_refund': return <ReceiptIcon />;
      case 'in_invoice': case 'in_refund': return <BillIcon />;
      default: return <AccountBalanceIcon />;
    }
  };

  const getMoveTypeLabel = (moveType: string) => {
    const types = accountMoveService.getMoveTypes();
    return types.find(t => t.value === moveType)?.label || moveType;
  };

  const handleInputChange = (field: keyof AccountMoveFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLineChange = (index: number, field: keyof AccountMoveLine, value: any) => {
    const newLines = [...formData.line_ids];
    newLines[index] = { ...newLines[index], [field]: value };
    setFormData(prev => ({ ...prev, line_ids: newLines }));
  };

  const addLine = () => {
    const newLine: Partial<AccountMoveLine> = {
      account_id: undefined,
      name: '',
      debit: '0.00',
      credit: '0.00',
    };
    setFormData(prev => ({
      ...prev,
      line_ids: [...prev.line_ids, newLine]
    }));
  };

  const removeLine = (index: number) => {
    if (formData.line_ids.length > 2) {
      const newLines = formData.line_ids.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, line_ids: newLines }));
    }
  };

  const calculateTotals = () => {
    const totalDebit = formData.line_ids.reduce((sum, line) => 
      sum + parseFloat(line.debit || '0'), 0
    );
    const totalCredit = formData.line_ids.reduce((sum, line) => 
      sum + parseFloat(line.credit || '0'), 0
    );
    return { totalDebit, totalCredit, difference: totalDebit - totalCredit };
  };

  const handleSave = async (post = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      if (!formData.journal_id) {
        throw new Error('Journal is required');
      }

      if (!formData.ref || formData.ref.trim() === '') {
        throw new Error('Reference is required');
      }

      // Validate lines
      if (formData.line_ids.length < 2) {
        throw new Error('At least 2 lines are required');
      }

      for (let i = 0; i < formData.line_ids.length; i++) {
        const line = formData.line_ids[i];

        if (!line.account_id) {
          throw new Error(`Account is required for line ${i + 1}`);
        }

        if (!line.name || line.name.trim() === '') {
          throw new Error(`Description is required for line ${i + 1}`);
        }

        const debit = parseFloat(line.debit || '0');
        const credit = parseFloat(line.credit || '0');

        if (debit === 0 && credit === 0) {
          throw new Error(`Line ${i + 1} must have either a debit or credit amount`);
        }

        if (debit > 0 && credit > 0) {
          throw new Error(`Line ${i + 1} cannot have both debit and credit amounts`);
        }
      }

      // Validate balance
      const { difference } = calculateTotals();
      if (Math.abs(difference) > 0.01) {
        throw new Error('Debits must equal Credits');
      }

      // Clean the line data - remove any lines with undefined account_id
      const cleanLines = formData.line_ids.filter(line => line.account_id !== undefined);

      const moveData: Partial<AccountMove> = {
        move_type: formData.move_type,
        ref: formData.ref,
        date: formData.date,
        partner_id: formData.partner_id,
        journal_id: formData.journal_id!,
        line_ids: cleanLines as AccountMoveLine[],
      };

      let result;
      if (id) {
        result = await accountMoveService.updateAccountMove(parseInt(id), moveData);
      } else {
        result = await accountMoveService.createAccountMove(moveData);
      }

      if (post && result.state === 'draft') {
        await accountMoveService.postAccountMove(result.id);
      }

      setSuccess(`Account move ${id ? 'updated' : 'created'} successfully!`);
      
      setTimeout(() => {
        navigate('/dashboard/gl/account-moves');
      }, 1500);

    } catch (err) {
      console.error('Error saving account move:', err);
      setError(err instanceof Error ? err.message : 'Failed to save account move');
    } finally {
      setLoading(false);
    }
  };

  const { totalDebit, totalCredit, difference } = calculateTotals();
  const isBalanced = Math.abs(difference) < 0.01;

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper elevation={1} sx={{ p: 2, borderRadius: 0 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton onClick={() => navigate('/dashboard/gl/account-moves')}>
              <ArrowBackIcon />
            </IconButton>
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                {getMoveTypeIcon(formData.move_type)}
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {id ? 'Edit' : 'Create'} {getMoveTypeLabel(formData.move_type)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formData.name !== '/' ? `Move #${formData.name}` : 'New Move'}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box display="flex" gap={1}>
            <Chip
              label={formData.state.toUpperCase()}
              color={formData.state === 'posted' ? 'success' : 'warning'}
              size="small"
            />
            {formData.state === 'draft' && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<SaveIcon />}
                  onClick={() => handleSave(false)}
                  disabled={loading || !isBalanced}
                >
                  Save Draft
                </Button>
                <Button
                  variant="contained"
                  startIcon={<PostIcon />}
                  onClick={() => handleSave(true)}
                  disabled={loading || !isBalanced}
                >
                  Save & Post
                </Button>
              </>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Move Details */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Move Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Move Type</InputLabel>
                      <Select
                        value={formData.move_type}
                        onChange={(e) => handleInputChange('move_type', e.target.value)}
                        disabled={!!id}
                        label="Move Type"
                      >
                        {accountMoveService.getMoveTypes().map(type => (
                          <MenuItem key={type.value} value={type.value}>
                            {type.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <StandardDatePicker
                      label="Date *"
                      value={dayjs(formData.date)}
                      onChange={(newValue) => handleInputChange('date', newValue?.format('YYYY-MM-DD') || '')}
                      disabled={formData.state === 'posted'}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Journal</InputLabel>
                      <Select
                        value={formData.journal_id || ''}
                        onChange={(e) => handleInputChange('journal_id', e.target.value)}
                        disabled={formData.state === 'posted'}
                        label="Journal"
                      >
                        {journals.map(journal => (
                          <MenuItem key={journal.id} value={journal.id}>
                            {journal.code} - {journal.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Reference"
                      value={formData.ref}
                      onChange={(e) => handleInputChange('ref', e.target.value)}
                      disabled={formData.state === 'posted'}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Balance Summary */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Balance Summary
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Total Debits:</Typography>
                  <Typography>{formatCurrency(totalDebit)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Total Credits:</Typography>
                  <Typography>{formatCurrency(totalCredit)}</Typography>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6" color={isBalanced ? 'success.main' : 'error.main'}>
                    Difference:
                  </Typography>
                  <Typography variant="h6" color={isBalanced ? 'success.main' : 'error.main'}>
                    {formatCurrency(Math.abs(difference))}
                  </Typography>
                </Box>
                {!isBalanced && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    Debits must equal Credits to post this move
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Journal Entry Lines */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Journal Entry Lines</Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={addLine}
                    disabled={formData.state === 'posted'}
                  >
                    Add Line
                  </Button>
                </Box>

                {formData.line_ids.map((line, index) => (
                  <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Account *</InputLabel>
                          <Select
                            value={line.account_id || ''}
                            onChange={(e) => handleLineChange(index, 'account_id', e.target.value)}
                            disabled={formData.state === 'posted'}
                            label="Account *"
                          >
                            {accounts.map(account => (
                              <MenuItem key={account.id} value={account.id}>
                                {account.account_number} - {account.account_name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          size="small"
                          label="Description *"
                          value={line.name || ''}
                          onChange={(e) => handleLineChange(index, 'name', e.target.value)}
                          disabled={formData.state === 'posted'}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          fullWidth
                          size="small"
                          label="Debit"
                          type="number"
                          value={line.debit || '0.00'}
                          onChange={(e) => handleLineChange(index, 'debit', e.target.value)}
                          disabled={formData.state === 'posted'}
                          inputProps={{ step: '0.01', min: '0' }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          fullWidth
                          size="small"
                          label="Credit"
                          type="number"
                          value={line.credit || '0.00'}
                          onChange={(e) => handleLineChange(index, 'credit', e.target.value)}
                          disabled={formData.state === 'posted'}
                          inputProps={{ step: '0.01', min: '0' }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Box display="flex" justifyContent="center">
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => removeLine(index)}
                            disabled={formData.state === 'posted' || formData.line_ids.length <= 2}
                          >
                            <CancelIcon />
                          </IconButton>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbars */}
      <Snackbar
        open={Boolean(success)}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
      >
        <Alert severity="success" onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AccountMoveFormPage;
