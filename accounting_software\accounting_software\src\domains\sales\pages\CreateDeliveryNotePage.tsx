import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Autocomplete,
  IconButton,
  Paper,
  Alert,
  Snackbar,
  Avatar,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Cancel as CancelIcon,
  LocalShipping as DeliveryIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { StandardDatePicker } from '../../../shared/components';
import { goodsDeliveryNoteService, GoodsDeliveryNote } from '../../../services/goodsDeliveryNote.service';
import salesOrderService from '../../../services/salesOrder.service';
import { useCustomers } from '../../../contexts/CustomerContext';

interface DeliveryNoteFormData {
  sales_order: number | null;
  customer: number | null;
  delivery_date: string;
  expected_delivery_date: string;
  delivery_address: string;
  delivery_contact_person: string;
  delivery_contact_phone: string;
  vehicle_number: string;
  driver_name: string;
  driver_phone: string;
  notes: string;
  internal_notes: string;
  status: 'draft' | 'confirmed' | 'delivered' | 'cancelled';
}

interface CreateDeliveryNotePageProps {
  deliveryNote?: GoodsDeliveryNote | null;
  viewMode?: boolean;
  onClose?: () => void;
  salesOrderId?: number;
}

const CreateDeliveryNotePage: React.FC<CreateDeliveryNotePageProps> = ({
  deliveryNote,
  viewMode = false,
  onClose,
  salesOrderId,
}) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { customers, loading: customersLoading } = useCustomers();

  // Determine if this is edit mode
  const isEditMode = location.pathname.includes('/edit') || !!deliveryNote;
  const deliveryNoteId = id && !isNaN(parseInt(id)) ? parseInt(id) : null;
  
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [salesOrders, setSalesOrders] = useState<any[]>([]);
  const [salesOrdersLoading, setSalesOrdersLoading] = useState(true);
  const [gdnNumber, setGdnNumber] = useState<string>('');
  const [currentDeliveryNote, setCurrentDeliveryNote] = useState<GoodsDeliveryNote | null>(deliveryNote || null);

  // Form data
  const [formData, setFormData] = useState<DeliveryNoteFormData>({
    sales_order: salesOrderId || currentDeliveryNote?.sales_order || null,
    customer: currentDeliveryNote?.customer || null,
    delivery_date: currentDeliveryNote?.delivery_date || dayjs().format('YYYY-MM-DD'),
    expected_delivery_date: currentDeliveryNote?.expected_delivery_date || dayjs().add(1, 'day').format('YYYY-MM-DD'),
    delivery_address: currentDeliveryNote?.delivery_address || '',
    delivery_contact_person: currentDeliveryNote?.delivery_contact_person || '',
    delivery_contact_phone: currentDeliveryNote?.delivery_contact_phone || '',
    vehicle_number: currentDeliveryNote?.vehicle_number || '',
    driver_name: currentDeliveryNote?.driver_name || '',
    driver_phone: currentDeliveryNote?.driver_phone || '',
    notes: currentDeliveryNote?.notes || '',
    internal_notes: currentDeliveryNote?.internal_notes || '',
    status: currentDeliveryNote?.status || 'draft',
  });

  // Load delivery note data in edit mode
  useEffect(() => {
    const loadDeliveryNote = async () => {
      if (isEditMode && deliveryNoteId && !currentDeliveryNote) {
        try {
          setLoading(true);
          const data = await goodsDeliveryNoteService.getById(deliveryNoteId);
          setCurrentDeliveryNote(data);

          // Update form data with loaded delivery note
          setFormData({
            sales_order: data.sales_order,
            customer: data.customer,
            delivery_date: data.delivery_date,
            expected_delivery_date: data.expected_delivery_date || '',
            delivery_address: data.delivery_address || '',
            delivery_contact_person: data.delivery_contact_person || '',
            delivery_contact_phone: data.delivery_contact_phone || '',
            vehicle_number: data.vehicle_number || '',
            driver_name: data.driver_name || '',
            driver_phone: data.driver_phone || '',
            notes: data.notes || '',
            internal_notes: data.internal_notes || '',
            status: data.status,
          });

          setGdnNumber(data.gdn_number || '');
        } catch (err: any) {
          setError(err.message || 'Failed to load delivery note');
        } finally {
          setLoading(false);
        }
      }
    };

    loadDeliveryNote();
  }, [isEditMode, deliveryNoteId, currentDeliveryNote]);

  // Load sales orders
  useEffect(() => {
    const loadSalesOrders = async () => {
      try {
        setSalesOrdersLoading(true);
        const response = await salesOrderService.getAll();
        const orders = response.results || response;
        // In edit mode, include all orders; in create mode, filter for available orders
        const availableOrders = isEditMode ? orders : orders.filter((order: any) =>
          ['pending', 'acknowledged', 'partial'].includes(order.status)
        );
        setSalesOrders(availableOrders);
      } catch (err) {
        console.error('Failed to load sales orders:', err);
      } finally {
        setSalesOrdersLoading(false);
      }
    };

    loadSalesOrders();
  }, [isEditMode]);

  // Set GDN number for display
  useEffect(() => {
    if (currentDeliveryNote?.gdn_number) {
      setGdnNumber(currentDeliveryNote.gdn_number);
    } else {
      setGdnNumber('Will be auto-generated');
    }
  }, [currentDeliveryNote]);

  // Auto-fill customer when sales order is selected
  useEffect(() => {
    if (formData.sales_order) {
      const selectedOrder = salesOrders.find(order => order.id === formData.sales_order);
      if (selectedOrder) {
        setFormData(prev => ({
          ...prev,
          customer: selectedOrder.customer_id || selectedOrder.customer,
          delivery_address: selectedOrder.ship_to_address || '',
        }));
      }
    }
  }, [formData.sales_order, salesOrders]);

  const handleInputChange = (field: keyof DeliveryNoteFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): string | null => {
    if (!formData.sales_order) return 'Sales order is required';
    if (!formData.delivery_date) return 'Delivery date is required';
    if (!formData.delivery_address) return 'Delivery address is required';
    return null;
  };

  const handleSave = async (action: 'save' | 'save-close' | 'save-new' = 'save') => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let result: GoodsDeliveryNote;
      if (currentDeliveryNote?.id) {
        result = await goodsDeliveryNoteService.update(currentDeliveryNote.id, formData);
        setSuccess('Delivery note updated successfully');
      } else {
        result = await goodsDeliveryNoteService.create(formData);
        setSuccess('Delivery note created successfully');
      }

      if (action === 'save-close') {
        navigate('/dashboard/sales/delivery-notes');
      } else if (action === 'save-new') {
        // Reset form for new delivery note
        setFormData({
          sales_order: null,
          customer: null,
          delivery_date: dayjs().format('YYYY-MM-DD'),
          expected_delivery_date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
          delivery_address: '',
          delivery_contact_person: '',
          delivery_contact_phone: '',
          vehicle_number: '',
          driver_name: '',
          driver_phone: '',
          notes: '',
          internal_notes: '',
          status: 'draft',
        });
        setGdnNumber('Will be auto-generated');
      } else {
        // Navigate to the created/updated delivery note
        navigate(`/dashboard/sales/delivery-notes/${result.id}`);
      }
    } catch (err: any) {
      console.error('Error saving delivery note:', err);
      setError(err.message || 'Failed to save delivery note');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onClose) {
      onClose();
    } else {
      navigate('/dashboard/sales/delivery-notes');
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper elevation={1} sx={{ p: 2, borderRadius: 0 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton onClick={handleCancel}>
              <ArrowBackIcon />
            </IconButton>
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <DeliveryIcon />
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {viewMode ? 'View' : (currentDeliveryNote ? 'Edit' : 'Create')} Delivery Note
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {gdnNumber && `GDN #${gdnNumber}`}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Action Buttons */}
          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            {!viewMode && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<SaveIcon />}
                  onClick={() => handleSave('save')}
                  disabled={loading}
                >
                  Save
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SaveAltIcon />}
                  onClick={() => handleSave('save-new')}
                  disabled={loading}
                >
                  Save & New
                </Button>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={() => handleSave('save-close')}
                  disabled={loading}
                >
                  Save & Close
                </Button>
              </>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AssignmentIcon />
                  Delivery Information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  {/* Sales Order Selection */}
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={salesOrders}
                      getOptionLabel={(option) => `${option.so_number} - ${option.customer_name || 'Unknown Customer'}`}
                      value={salesOrders.find(order => order.id === formData.sales_order) || null}
                      onChange={(_, value) => handleInputChange('sales_order', value?.id || null)}
                      loading={salesOrdersLoading}
                      disabled={viewMode}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Sales Order *"
                          required
                          error={!formData.sales_order}
                          helperText={!formData.sales_order ? 'Sales order is required' : ''}
                        />
                      )}
                    />
                  </Grid>

                  {/* Customer (Auto-filled) */}
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={customers}
                      getOptionLabel={(option) => option.display_name || option.name || ''}
                      value={customers.find(customer => customer.id === formData.customer) || null}
                      onChange={(_, value) => handleInputChange('customer', value?.id || null)}
                      loading={customersLoading}
                      disabled={true} // Auto-filled from sales order
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer"
                          helperText="Auto-filled from sales order"
                        />
                      )}
                    />
                  </Grid>

                  {/* Delivery Date */}
                  <Grid item xs={12} md={6}>
                    <StandardDatePicker
                      label="Delivery Date *"
                      value={formData.delivery_date}
                      onChange={(value) => handleInputChange('delivery_date', value)}
                      disabled={viewMode}
                      required
                    />
                  </Grid>

                  {/* Expected Delivery Date */}
                  <Grid item xs={12} md={6}>
                    <StandardDatePicker
                      label="Expected Delivery Date"
                      value={formData.expected_delivery_date}
                      onChange={(value) => handleInputChange('expected_delivery_date', value)}
                      disabled={viewMode}
                    />
                  </Grid>

                  {/* Delivery Address */}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Delivery Address *"
                      value={formData.delivery_address}
                      onChange={(e) => handleInputChange('delivery_address', e.target.value)}
                      disabled={viewMode}
                      required
                      multiline
                      rows={3}
                      error={!formData.delivery_address}
                      helperText={!formData.delivery_address ? 'Delivery address is required' : ''}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            {/* Contact Information */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Contact Information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Contact Person"
                      value={formData.delivery_contact_person}
                      onChange={(e) => handleInputChange('delivery_contact_person', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      value={formData.delivery_contact_phone}
                      onChange={(e) => handleInputChange('delivery_contact_phone', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Vehicle Information */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Vehicle Information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Vehicle Number"
                      value={formData.vehicle_number}
                      onChange={(e) => handleInputChange('vehicle_number', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Driver Name"
                      value={formData.driver_name}
                      onChange={(e) => handleInputChange('driver_name', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Driver Phone"
                      value={formData.driver_phone}
                      onChange={(e) => handleInputChange('driver_phone', e.target.value)}
                      disabled={viewMode}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Status */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Status & Notes
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={formData.status}
                        onChange={(e) => handleInputChange('status', e.target.value)}
                        disabled={viewMode}
                        label="Status"
                      >
                        <MenuItem value="draft">Draft</MenuItem>
                        <MenuItem value="confirmed">Confirmed</MenuItem>
                        <MenuItem value="delivered">Delivered</MenuItem>
                        <MenuItem value="cancelled">Cancelled</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      disabled={viewMode}
                      multiline
                      rows={3}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Internal Notes"
                      value={formData.internal_notes}
                      onChange={(e) => handleInputChange('internal_notes', e.target.value)}
                      disabled={viewMode}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Success Snackbar */}
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateDeliveryNotePage;
