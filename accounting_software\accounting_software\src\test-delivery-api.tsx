import React, { useState } from 'react';
import { <PERSON>ton, Box, Typography, Paper } from '@mui/material';
import { goodsDeliveryNoteService } from './services/goodsDeliveryNote.service';

const TestDeliveryAPI: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testGetAll = async () => {
    setLoading(true);
    try {
      console.log('Testing getAll...');
      const data = await goodsDeliveryNoteService.getAll();
      console.log('getAll result:', data);
      setResult(`getAll Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      console.error('getAll error:', error);
      setResult(`getAll Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testGetById = async () => {
    setLoading(true);
    try {
      console.log('Testing getById(1)...');
      const data = await goodsDeliveryNoteService.getById(1);
      console.log('getById result:', data);
      setResult(`getById Success: ${JSON.stringify(data, null, 2)}`);
    } catch (error: any) {
      console.error('getById error:', error);
      setResult(`getById Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAuth = () => {
    const token = localStorage.getItem('token');
    console.log('Auth token:', token);
    setResult(`Auth token: ${token ? 'Present' : 'Missing'}\nToken: ${token}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Delivery Notes API Test
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button variant="contained" onClick={testAuth}>
          Check Auth
        </Button>
        <Button variant="contained" onClick={testGetAll} disabled={loading}>
          Test Get All
        </Button>
        <Button variant="contained" onClick={testGetById} disabled={loading}>
          Test Get By ID (1)
        </Button>
      </Box>

      <Paper sx={{ p: 2, bgcolor: '#f5f5f5' }}>
        <Typography variant="h6" gutterBottom>
          Result:
        </Typography>
        <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.875rem' }}>
          {result || 'No test run yet'}
        </Typography>
      </Paper>
    </Box>
  );
};

export default TestDeliveryAPI;
