/**
 * Invoice Policy Service - Odoo-style Invoice Management
 * Handles invoice creation based on ordered vs delivered quantities
 */

import api from './api';
import { OdooSalesOrder, SalesOrderLine, CustomerInvoice, InvoiceLine, InvoicePolicy } from './odoo-sales-order.service';

export interface InvoiceableLines {
  line_id: number;
  product_id: number;
  product_name: string;
  description: string;
  ordered_qty: number;
  delivered_qty: number;
  invoiced_qty: number;
  qty_to_invoice: number;
  unit_price: number;
  discount: number;
  subtotal: number;
  can_invoice: boolean;
  invoice_policy: InvoicePolicy;
}

export interface InvoiceCreationOptions {
  invoice_policy?: InvoicePolicy;
  invoice_date?: string;
  payment_terms?: string;
  lines?: {
    line_id: number;
    qty_to_invoice: number;
  }[];
  advance_payment?: {
    amount: number;
    account_id: number;
    description: string;
  };
}

export interface InvoicePreview {
  lines: InvoiceableLines[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency: string;
  can_create_invoice: boolean;
  warnings: string[];
}

class InvoicePolicyService {
  private baseUrl = '/api/sales';

  /**
   * Get invoiceable lines for a sales order based on invoice policy
   */
  async getInvoiceableLines(salesOrderId: number, invoicePolicy?: InvoicePolicy): Promise<InvoiceableLines[]> {
    const params = new URLSearchParams();
    if (invoicePolicy) params.append('invoice_policy', invoicePolicy);
    
    const response = await api.get(`${this.baseUrl}/orders/${salesOrderId}/invoiceable-lines/?${params.toString()}`);
    return response.data.results || response.data;
  }

  /**
   * Preview invoice before creation
   */
  async previewInvoice(salesOrderId: number, options?: InvoiceCreationOptions): Promise<InvoicePreview> {
    const response = await api.post(`${this.baseUrl}/orders/${salesOrderId}/preview-invoice/`, options);
    return response.data;
  }

  /**
   * Create invoice from sales order based on policy
   */
  async createInvoiceFromSalesOrder(salesOrderId: number, options?: InvoiceCreationOptions): Promise<{
    invoice: CustomerInvoice;
    updated_sales_order: OdooSalesOrder;
    warnings: string[];
  }> {
    const response = await api.post(`${this.baseUrl}/orders/${salesOrderId}/create-invoice/`, options);
    return response.data;
  }

  /**
   * Create advance payment invoice (down payment)
   */
  async createAdvancePaymentInvoice(salesOrderId: number, data: {
    amount: number;
    account_id: number;
    description: string;
    invoice_date?: string;
  }): Promise<CustomerInvoice> {
    const response = await api.post(`${this.baseUrl}/orders/${salesOrderId}/create-advance-invoice/`, data);
    return response.data;
  }

  /**
   * Update invoice quantities (for partial invoicing)
   */
  async updateInvoiceQuantities(invoiceId: number, lines: {
    line_id: number;
    quantity: number;
  }[]): Promise<CustomerInvoice> {
    const response = await api.put(`${this.baseUrl}/invoices/${invoiceId}/update-quantities/`, { lines });
    return response.data;
  }

  /**
   * Calculate invoice amounts based on policy
   */
  calculateInvoiceAmounts(lines: SalesOrderLine[], invoicePolicy: InvoicePolicy): {
    subtotal: number;
    tax_amount: number;
    total_amount: number;
    invoiceable_lines: number;
  } {
    let subtotal = 0;
    let tax_amount = 0;
    let invoiceable_lines = 0;

    lines.forEach(line => {
      let qty_to_invoice = 0;
      
      if (invoicePolicy === 'order') {
        // Invoice based on ordered quantities
        qty_to_invoice = line.product_uom_qty - line.qty_invoiced;
      } else {
        // Invoice based on delivered quantities
        qty_to_invoice = line.qty_delivered - line.qty_invoiced;
      }

      if (qty_to_invoice > 0) {
        const line_subtotal = qty_to_invoice * line.price_unit * (1 - line.discount / 100);
        const line_tax = line_subtotal * 0.1; // Simplified tax calculation
        
        subtotal += line_subtotal;
        tax_amount += line_tax;
        invoiceable_lines++;
      }
    });

    return {
      subtotal,
      tax_amount,
      total_amount: subtotal + tax_amount,
      invoiceable_lines,
    };
  }

  /**
   * Check if sales order can be invoiced
   */
  canCreateInvoice(salesOrder: OdooSalesOrder): {
    can_invoice: boolean;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let can_invoice = true;

    // Check sales order state
    if (salesOrder.state !== 'sale' && salesOrder.state !== 'done') {
      can_invoice = false;
      reasons.push('Sales order must be confirmed before invoicing');
    }

    // Check if there are invoiceable quantities
    const hasInvoiceableQty = salesOrder.order_line.some(line => {
      if (salesOrder.invoice_policy === 'order') {
        return line.product_uom_qty > line.qty_invoiced;
      } else {
        return line.qty_delivered > line.qty_invoiced;
      }
    });

    if (!hasInvoiceableQty) {
      can_invoice = false;
      reasons.push('No quantities available for invoicing');
    }

    return { can_invoice, reasons };
  }

  /**
   * Get invoice status for sales order
   */
  getInvoiceStatus(salesOrder: OdooSalesOrder): {
    status: 'nothing' | 'partial' | 'full';
    percentage: number;
    amount_invoiced: number;
    amount_to_invoice: number;
  } {
    const total_amount = salesOrder.amount_total;
    const amount_invoiced = salesOrder.amount_invoiced || 0;
    const amount_to_invoice = total_amount - amount_invoiced;
    
    let status: 'nothing' | 'partial' | 'full' = 'nothing';
    let percentage = 0;

    if (amount_invoiced > 0) {
      percentage = (amount_invoiced / total_amount) * 100;
      status = percentage >= 100 ? 'full' : 'partial';
    }

    return {
      status,
      percentage: Math.round(percentage),
      amount_invoiced,
      amount_to_invoice,
    };
  }

  /**
   * Auto-invoice based on delivery (for invoice_policy = 'delivery')
   */
  async autoInvoiceOnDelivery(deliveryOrderId: number): Promise<{
    invoice?: CustomerInvoice;
    sales_order: OdooSalesOrder;
    created: boolean;
    reason?: string;
  }> {
    const response = await api.post(`${this.baseUrl}/deliveries/${deliveryOrderId}/auto-invoice/`);
    return response.data;
  }

  /**
   * Batch invoice creation for multiple sales orders
   */
  async batchCreateInvoices(salesOrderIds: number[], options?: {
    invoice_policy?: InvoicePolicy;
    invoice_date?: string;
    group_by_partner?: boolean;
  }): Promise<{
    invoices: CustomerInvoice[];
    errors: { sales_order_id: number; error: string }[];
    warnings: string[];
  }> {
    const response = await api.post(`${this.baseUrl}/batch-create-invoices/`, {
      sales_order_ids: salesOrderIds,
      ...options,
    });
    return response.data;
  }

  /**
   * Get invoice policy recommendations based on business rules
   */
  getInvoicePolicyRecommendation(salesOrder: OdooSalesOrder): {
    recommended_policy: InvoicePolicy;
    reason: string;
    considerations: string[];
  } {
    const considerations: string[] = [];
    let recommended_policy: InvoicePolicy = 'order';
    let reason = '';

    // Check if products are services or goods
    const hasServices = salesOrder.order_line.some(line => 
      line.product_name?.toLowerCase().includes('service') ||
      line.name?.toLowerCase().includes('service')
    );

    const hasGoods = salesOrder.order_line.some(line => 
      !line.product_name?.toLowerCase().includes('service') &&
      !line.name?.toLowerCase().includes('service')
    );

    if (hasServices && !hasGoods) {
      recommended_policy = 'order';
      reason = 'Services are typically invoiced when ordered';
      considerations.push('Services don\'t require physical delivery');
    } else if (hasGoods && !hasServices) {
      recommended_policy = 'delivery';
      reason = 'Physical goods should be invoiced after delivery';
      considerations.push('Ensures customer receives goods before payment');
      considerations.push('Reduces disputes and returns');
    } else {
      recommended_policy = 'order';
      reason = 'Mixed orders typically use order-based invoicing';
      considerations.push('Services can be invoiced immediately');
      considerations.push('Goods portion can be invoiced on delivery if needed');
    }

    // Check customer payment terms
    if (salesOrder.payment_term_id) {
      considerations.push('Consider customer payment terms when choosing policy');
    }

    return {
      recommended_policy,
      reason,
      considerations,
    };
  }

  /**
   * Get invoice policy options with descriptions
   */
  getInvoicePolicyOptions(): Array<{
    value: InvoicePolicy;
    label: string;
    description: string;
    use_cases: string[];
  }> {
    return [
      {
        value: 'order',
        label: 'Ordered quantities',
        description: 'Invoice based on quantities ordered, regardless of delivery status',
        use_cases: [
          'Service products',
          'Advance payments required',
          'Digital products',
          'Custom manufacturing'
        ],
      },
      {
        value: 'delivery',
        label: 'Delivered quantities',
        description: 'Invoice only after products are delivered to customer',
        use_cases: [
          'Physical goods',
          'Inventory items',
          'Partial deliveries expected',
          'Quality control required'
        ],
      },
    ];
  }
}

export const invoicePolicyService = new InvoicePolicyService();
export default invoicePolicyService;
