/**
 * Odoo-Style Sales Order Service
 * Implements Odoo's sales methodology: Sales Order → Delivery → Invoice
 */

import api from './api';

// Odoo-style Sales Order States
export type SalesOrderState = 
  | 'draft'      // Draft - can be edited
  | 'sent'       // Quotation sent to customer
  | 'sale'       // Sales order confirmed
  | 'done'       // Fully delivered and invoiced
  | 'cancel';    // Cancelled

// Invoice Policy (Odoo pattern)
export type InvoicePolicy = 
  | 'order'      // Invoice ordered quantities
  | 'delivery';  // Invoice delivered quantities

// Delivery Policy
export type DeliveryPolicy = 
  | 'direct'     // Deliver each product when available
  | 'one';       // Deliver all products at once

export interface OdooSalesOrder {
  id?: number;
  name: string;                    // SO number (auto-generated)
  partner_id: number;              // Customer
  partner_name?: string;
  date_order: string;              // Order date
  validity_date?: string;          // Quotation expiry
  commitment_date?: string;        // Delivery commitment
  
  // States and Policies
  state: SalesOrderState;
  invoice_policy: InvoicePolicy;
  delivery_policy: DeliveryPolicy;
  
  // Financial Information
  amount_untaxed: number;          // Subtotal
  amount_tax: number;              // Tax amount
  amount_total: number;            // Total amount
  amount_invoiced: number;         // Invoiced amount
  amount_to_invoice: number;       // Remaining to invoice
  
  // Delivery Information
  delivery_count: number;          // Number of deliveries
  invoice_count: number;           // Number of invoices
  
  // Line Items
  order_line: SalesOrderLine[];
  
  // Additional Fields
  client_order_ref?: string;       // Customer reference
  note?: string;                   // Terms and conditions
  payment_term_id?: number;        // Payment terms
  pricelist_id?: number;           // Price list
  
  // Metadata
  user_id?: number;                // Salesperson
  team_id?: number;                // Sales team
  company_id?: number;             // Company
  currency_id?: number;            // Currency
  
  create_date?: string;
  write_date?: string;
}

export interface SalesOrderLine {
  id?: number;
  order_id?: number;
  product_id: number;
  product_name?: string;
  name: string;                    // Description
  product_uom_qty: number;         // Ordered quantity
  qty_delivered: number;           // Delivered quantity
  qty_invoiced: number;            // Invoiced quantity
  qty_to_invoice: number;          // Remaining to invoice
  price_unit: number;              // Unit price
  discount: number;                // Discount %
  price_subtotal: number;          // Line subtotal
  price_total: number;             // Line total with tax
  tax_id?: number[];               // Applied taxes
  product_uom?: string;            // Unit of measure
  sequence: number;                // Line order
}

export interface DeliveryOrder {
  id?: number;
  name: string;                    // Delivery reference
  origin: string;                  // Source document (SO number)
  partner_id: number;              // Customer
  scheduled_date: string;          // Scheduled delivery
  date_done?: string;              // Actual delivery date
  state: 'draft' | 'waiting' | 'confirmed' | 'assigned' | 'done' | 'cancel';
  location_id: number;             // Source location
  location_dest_id: number;        // Destination location
  move_lines: StockMove[];         // Stock moves
}

export interface StockMove {
  id?: number;
  name: string;                    // Description
  product_id: number;
  product_uom_qty: number;         // Demand
  quantity_done: number;           // Done quantity
  state: 'draft' | 'waiting' | 'confirmed' | 'assigned' | 'done' | 'cancel';
  location_id: number;
  location_dest_id: number;
  sale_line_id?: number;           // Link to sales order line
}

export interface CustomerInvoice {
  id?: number;
  name: string;                    // Invoice number
  partner_id: number;              // Customer
  invoice_date: string;
  invoice_date_due?: string;
  state: 'draft' | 'posted' | 'paid' | 'cancel';
  move_type: 'out_invoice' | 'out_refund';
  amount_untaxed: number;
  amount_tax: number;
  amount_total: number;
  amount_residual: number;         // Amount due
  invoice_origin?: string;         // Source document
  invoice_line_ids: InvoiceLine[];
}

export interface InvoiceLine {
  id?: number;
  product_id: number;
  name: string;                    // Description
  quantity: number;
  price_unit: number;
  discount: number;
  price_subtotal: number;
  price_total: number;
  tax_ids?: number[];
  sale_line_ids?: number[];        // Link to sales order lines
}

class OdooSalesOrderService {
  private baseUrl = '/api/sales';

  // Sales Order CRUD Operations
  async getSalesOrders(filters?: any): Promise<{ results: OdooSalesOrder[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters?.state) params.append('state', filters.state);
    if (filters?.partner_id) params.append('partner_id', filters.partner_id.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/orders/?${params.toString()}`);
    return {
      results: response.data.results || [],
      count: response.data.count || 0
    };
  }

  async getSalesOrder(id: number): Promise<OdooSalesOrder> {
    const response = await api.get(`${this.baseUrl}/orders/${id}/`);
    return response.data;
  }

  async createSalesOrder(data: Partial<OdooSalesOrder>): Promise<OdooSalesOrder> {
    const response = await api.post(`${this.baseUrl}/orders/`, {
      ...data,
      state: 'draft',
      invoice_policy: data.invoice_policy || 'order',
      delivery_policy: data.delivery_policy || 'direct',
    });
    return response.data;
  }

  async updateSalesOrder(id: number, data: Partial<OdooSalesOrder>): Promise<OdooSalesOrder> {
    const response = await api.put(`${this.baseUrl}/orders/${id}/`, data);
    return response.data;
  }

  async deleteSalesOrder(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/orders/${id}/`);
  }

  // Odoo-style State Transitions
  async actionQuotationSent(id: number): Promise<OdooSalesOrder> {
    const response = await api.post(`${this.baseUrl}/orders/${id}/action_quotation_sent/`);
    return response.data;
  }

  async actionConfirm(id: number): Promise<OdooSalesOrder> {
    // Confirms the sales order (draft/sent → sale)
    // This should trigger:
    // 1. Stock reservation
    // 2. Delivery order creation
    // 3. Invoice creation (if invoice_policy = 'order')
    const response = await api.post(`${this.baseUrl}/orders/${id}/action_confirm/`);
    return response.data;
  }

  async actionCancel(id: number): Promise<OdooSalesOrder> {
    const response = await api.post(`${this.baseUrl}/orders/${id}/action_cancel/`);
    return response.data;
  }

  async actionDone(id: number): Promise<OdooSalesOrder> {
    // Marks order as done (fully delivered and invoiced)
    const response = await api.post(`${this.baseUrl}/orders/${id}/action_done/`);
    return response.data;
  }

  // Delivery Operations
  async getDeliveryOrders(salesOrderId: number): Promise<DeliveryOrder[]> {
    const response = await api.get(`${this.baseUrl}/orders/${salesOrderId}/deliveries/`);
    return response.data.results || response.data;
  }

  async createDeliveryOrder(salesOrderId: number, data?: any): Promise<DeliveryOrder> {
    const response = await api.post(`${this.baseUrl}/orders/${salesOrderId}/create_delivery/`, data);
    return response.data;
  }

  async validateDelivery(deliveryId: number, moves: { move_id: number; quantity_done: number }[]): Promise<DeliveryOrder> {
    const response = await api.post(`${this.baseUrl}/deliveries/${deliveryId}/validate/`, { moves });
    return response.data;
  }

  // Invoice Operations
  async getInvoices(salesOrderId: number): Promise<CustomerInvoice[]> {
    const response = await api.get(`${this.baseUrl}/orders/${salesOrderId}/invoices/`);
    return response.data.results || response.data;
  }

  async createInvoice(salesOrderId: number, data?: {
    invoice_policy?: InvoicePolicy;
    lines?: { line_id: number; qty_to_invoice: number }[];
  }): Promise<CustomerInvoice> {
    const response = await api.post(`${this.baseUrl}/orders/${salesOrderId}/create_invoice/`, data);
    return response.data;
  }

  // Utility Methods
  async getAvailableProducts(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/products/`);
    return response.data.results || response.data;
  }

  async getCustomers(): Promise<any[]> {
    const response = await api.get('/api/contacts/customers/');
    return response.data.results || response.data;
  }

  async getPriceLists(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/pricelists/`);
    return response.data.results || response.data;
  }

  async getPaymentTerms(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/payment-terms/`);
    return response.data.results || response.data;
  }

  // Dashboard and Statistics
  async getSalesStats(): Promise<any> {
    const response = await api.get(`${this.baseUrl}/stats/`);
    return response.data;
  }

  // State Labels for UI
  getStateLabel(state: SalesOrderState): string {
    const labels = {
      draft: 'Quotation',
      sent: 'Quotation Sent',
      sale: 'Sales Order',
      done: 'Locked',
      cancel: 'Cancelled'
    };
    return labels[state] || state;
  }

  getStateColor(state: SalesOrderState): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' {
    const colors = {
      draft: 'default',
      sent: 'info',
      sale: 'primary',
      done: 'success',
      cancel: 'error'
    };
    return colors[state] || 'default';
  }
}

export const odooSalesOrderService = new OdooSalesOrderService();
export default odooSalesOrderService;
